from typing import Annotated,Dict, Any, Optional,AsyncIterator, <PERSON><PERSON>
from fastapi import  Request
from utils.common_utils import CommonUtils,stream_with_last_
from core.message.transmitter import Transmitter
import json
from mcpclient.llm_client import LLMClient
from core.config.app_logger import logger

################################
# 动态页面创建，修改智能体
# agent 构造器（提示词模版，大模型实例，执行器） 
# <AUTHOR> xiangjh
################################
def process_chunk(chunk: Dict[str, Any]) -> Optional[str]:
    """
    处理 chunk 数据，根据类型返回对应的内容。
    
    支持：
        - type == 'stream': 返回 content 文本
        - type == 'tool_start': 返回 XML 格式的工具调用字符串
    """
    chunk_type = chunk.get("type")
    
    if chunk_type == "stream":
        return chunk.get("content", "")
    
    elif chunk_type == "tool_start":
        tool_name = chunk.get("tool")
        if not tool_name:
            return None
        
        xml_data = f"""<message-embedded>
                            <widget>
                                <code>@BuildIn/Tool</code>
                                 <props>
                                    <name>{tool_name}</name>
                                </props>
                            </widget>
                        </message-embedded>"""
        return xml_data
    elif chunk_type == "error":
        return chunk.get("message", "")
    else:
        # 其他类型暂不处理
        return None

async def process_and_filter_chunks(stream: AsyncIterator[Any]) -> AsyncIterator[str]:
    async for chunk in stream:
        if isinstance(chunk, str):
            chunk = json.loads(chunk)
        processed_data = process_chunk(chunk)
        if processed_data:
            yield processed_data

# 事件生成器，用于流式响应，包含消息格式封装与发送
async def event_generator_and_send(transmitter: Transmitter, request: Request, question: str,user_id: str, agent_id: str, conversation_id: str):
    try: 
        yield transmitter.start()

        llm_client = LLMClient(request=request, user_id=user_id, agent_id=agent_id)
        await llm_client.init_async()
        
        session_id = CommonUtils.generate_session_id(user_id, agent_id,conversation_id)
        logger.info(f"会话开始：session_id: {session_id}")
        
        first_chunk = True
        final_question = question + "，请使用工具 "
       
        async for chunk, is_last in stream_with_last_(llm_client.chat_stream(question=final_question, session_id=session_id)):
            if isinstance(chunk, str):
                chunk = json.loads(chunk)

            processed_data = process_chunk(chunk)
            if processed_data:
                logger.debug(f"[SENDING] 发送 chunk: {processed_data}..., is_last={is_last}")
                yield transmitter.send_message(
                    data=processed_data,
                    package_type=0,
                    is_last=is_last,  
                    is_new_package=first_chunk,
                )
                first_chunk = False
            
        logger.info(f"会话结束：session_id: {session_id}")
        yield await transmitter.end()
    except Exception as e:
        error_msg = f"内部服务异常: {str(e)}"
        logger.error(error_msg, exc_info=True)  

        # 发送错误消息作为 chunk
        error_content = f"[ERROR] {error_msg}"
        yield transmitter.send_message(
            data=error_content,
            package_type=0,
            is_last=True,
            is_new_package=True
        )

        yield await transmitter.end()
    