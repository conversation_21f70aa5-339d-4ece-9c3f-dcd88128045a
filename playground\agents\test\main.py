import asyncio
from uuid import uuid4
from datetime import datetime, timezone

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends, FastAPI
from fastapi.responses import StreamingResponse


from core.config.security import check_user
from core.vo.user_vo import UserVO
from core.message.transmitter import Transmitter
from core.message.types import MessageType, MessagePackage
from core.services.database.schemas.message import MessageCreate
from core.services.database.schemas.conversation import ConversationCreate
from core.services.database import db_manager
from core.services.database.crud.conversation import conversation_curd
from core.services.database.crud.message import message_curd
from .messages.all import create_all_message_chunks
from .messages.choices import radio_choices_message_chunks, select_message_chunks
from .messages.mermaid import create_mermaid_message_chunks
from .messages.thinking import create_thinking_message_chunks
from .messages.tool import create_tool_message_chunks

chunks = {
    "all": create_all_message_chunks(),
    "radio_choices": radio_choices_message_chunks,
    "select_choices": select_message_chunks,
    "mermaid": create_mermaid_message_chunks(),
    "thinking": create_thinking_message_chunks(),
    "tool": create_tool_message_chunks(),
}

async def event_generator(transmitter: Transmitter, type: str = "all"):

    mock_message_chunks = chunks[type]
    yield transmitter.start()
    await asyncio.sleep(0.1)
    for chunk in mock_message_chunks:
        if chunk:
            # Convert the chunk to JSON and format as SSE
            yield transmitter.send_message(
                data=chunk["data"],
                package_type=chunk["package_type"],
                is_last=chunk["is_last"],
                is_new_package=chunk["is_new_package"],
            )
        await asyncio.sleep(0.1)

    # Send a final message to indicate completion
    yield await transmitter.end()


class TestAgent:
    app: FastAPI
    router: APIRouter

    def __init__(self, app: FastAPI):
        self.app = app
        self.router = APIRouter()
        self.add_api(self.router)
        app.include_router(self.router, tags=["Test"])

    def add_api(self, router: APIRouter):
        @router.post("/api/chat/test")
        async def test(
            data: dict = Body(..., description="用户提问内容"),
            user: UserVO = Depends(check_user),
        ):

            print(data)
            agent_code = data["agent_code"]
            # 根据conversation_id查询会话，如果没有则创建一个
            conversation_id = data.get("conversation_id")
            if not conversation_id:
                async with db_manager.session() as session:
                    new_conversation = ConversationCreate(
                        user_id=user.userId,
                        title="test",
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                        current_agent_code=agent_code,
                    )
                    new_conversation = await conversation_curd.create(
                        db=session, obj_input=new_conversation
                    )
                    conversation_id = new_conversation.id

            # 保存用户消息
            message_pkg = MessagePackage(
                package_id=0,
                package_type=0,
                status=1,
                data=data["message"],
            )
            async with db_manager.session() as session:
                new_message = MessageCreate(
                    agent_code=agent_code,
                    conversation_id=conversation_id,
                    message_type=MessageType.HUMAN,
                    content=[message_pkg.model_dump()],
                )
                await message_curd.create(db=session, obj_input=new_message)

                transmitter = Transmitter(
                    conversation_id=conversation_id,
                    message_id=new_message.id,
                    agent_code=agent_code,
                )

            return StreamingResponse(
                event_generator(transmitter, self.get_message_type(data["message"])),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                },
            )

    def get_message_type(self, user_message: str):
        msg = user_message.lower().strip()

        types = ["all", "radio_choices", "select_choices", "mermaid", "thinking", "tool"]
        for msg_type in types:
            if msg_type in msg:
                return msg_type
        return "radio_choices"
