from langchain_community.chat_message_histories import RedisChatMessageHistory
from langchain_core.messages import BaseMessage, HumanMessage
from core.services.database import db_manager
from core.services.database.schemas.message import MessageCreate
from datetime import datetime


# 自定义 Redis 消息历史记录，支持 PostgreSQL 同步写入
# <AUTHOR> xiangjh
class RedisWithPostgreSQLHistory(RedisChatMessageHistory):
    def __init__(
        self,
        session_id: str,
        user_id: str = None,
        agent_id: str = None,
        url: str = None,
        ttl: int = 3600,
    ):
        super().__init__(session_id=session_id, url=url, ttl=ttl)
        self.user_id = user_id
        self.agent_id = agent_id

    def add_message(self, message: BaseMessage) -> None:
        """同步方法：添加消息到 Redis，并同步写入 PostgreSQL"""
        super().add_message(message)

        #  同步写入 PostgreSQL 被移动到 接收消息的函数中，避免重复写入
