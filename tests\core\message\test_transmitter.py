"""
Transmitter 类的测试用例

测试 Transmitter 类的各个方法，包括初始化、start、send_message、end 和 create_chunk 方法。
"""

import unittest
import json

from core.message.transmitter import (
    Transmitter,
    EVENT_TYPE_START,
    EVENT_TYPE_LOADING,
    EVENT_TYPE_END,
    PACKAGE_TYPE_TEXT,
    PACKAGE_TYPE_STRUCTURED,
    PACKAGE_TYPE_NONE,
)

from core.message.types import Chunk


class TestTransmitter(unittest.TestCase):
    """Transmitter 类的测试用例"""

    def setUp(self):
        """每个测试用例执行前的设置"""
        self.conversation_id = "test_conversation_id"
        self.message_id = "test_message_id"
        self.transmitter = Transmitter(self.conversation_id, self.message_id)

    def test_init(self):
        """测试 Transmitter 初始化"""
        # 验证初始化后的属性值
        self.assertEqual(self.transmitter.conversation_id, self.conversation_id)
        self.assertEqual(self.transmitter.message_id, self.message_id)
        self.assertEqual(self.transmitter.event_id, -1)
        self.assertEqual(self.transmitter.event_type, EVENT_TYPE_START)
        self.assertEqual(self.transmitter.package_id, -1)
        self.assertEqual(self.transmitter.chunk_id, -1)

    def test_start(self):
        """测试 start 方法"""
        # 调用 start 方法
        chunk = self.transmitter.start()

        # 验证状态变化
        self.assertEqual(self.transmitter.event_id, 0)
        self.assertEqual(self.transmitter.event_type, EVENT_TYPE_START)
        self.assertEqual(self.transmitter.package_id, 0)
        self.assertEqual(self.transmitter.chunk_id, 0)
        self.assertEqual(self.transmitter.package_type, PACKAGE_TYPE_STRUCTURED)

        # 验证返回的 chunk 对象
        self.assertIsInstance(chunk, Chunk)
        self.assertEqual(chunk.event_id, 0)
        self.assertEqual(chunk.event_type, EVENT_TYPE_START)
        self.assertEqual(chunk.package_id, 0)
        self.assertEqual(chunk.package_type, PACKAGE_TYPE_STRUCTURED)
        self.assertEqual(chunk.chunk_id, 0)
        self.assertTrue(chunk.is_last)

        # 验证 chunk 的数据内容
        data = json.loads(chunk.data)
        self.assertEqual(data["type"], "header")
        self.assertEqual(data["conversation_id"], self.conversation_id)
        self.assertEqual(data["message_id"], self.message_id)

    def test_send_message_new_package(self):
        """测试 send_message 方法 - 新包"""
        # 先调用 start 初始化状态
        self.transmitter.start()

        # 测试发送新包
        test_data = "test message data"
        chunk = self.transmitter.send_message(
            test_data,
            package_type=PACKAGE_TYPE_TEXT,
            is_last=False,
            is_new_package=True,
        )

        # 验证状态变化
        self.assertEqual(self.transmitter.event_id, 1)
        self.assertEqual(self.transmitter.event_type, EVENT_TYPE_LOADING)
        self.assertEqual(self.transmitter.package_id, 1)
        self.assertEqual(self.transmitter.chunk_id, 0)
        self.assertEqual(self.transmitter.package_type, PACKAGE_TYPE_TEXT)

        # 验证返回的 chunk 对象
        self.assertIsInstance(chunk, Chunk)
        self.assertEqual(chunk.event_id, 1)
        self.assertEqual(chunk.event_type, EVENT_TYPE_LOADING)
        self.assertEqual(chunk.package_id, 1)
        self.assertEqual(chunk.package_type, PACKAGE_TYPE_TEXT)
        self.assertEqual(chunk.chunk_id, 0)
        self.assertFalse(chunk.is_last)
        self.assertEqual(chunk.data, test_data)

    def test_send_message_continue_package(self):
        """测试 send_message 方法 - 继续包"""
        # 先调用 start 初始化状态
        self.transmitter.start()

        # 先发送一个新包
        self.transmitter.send_message(
            "first chunk", package_type=PACKAGE_TYPE_TEXT, is_new_package=True
        )

        # 测试继续发送到同一个包
        test_data = "second chunk"
        chunk = self.transmitter.send_message(
            test_data,
            package_type=PACKAGE_TYPE_TEXT,
            is_last=True,
            is_new_package=False,
        )

        # 验证状态变化
        self.assertEqual(self.transmitter.event_id, 2)
        self.assertEqual(self.transmitter.event_type, EVENT_TYPE_LOADING)
        self.assertEqual(self.transmitter.package_id, 1)
        self.assertEqual(self.transmitter.chunk_id, 1)
        self.assertEqual(self.transmitter.package_type, PACKAGE_TYPE_TEXT)

        # 验证返回的 chunk 对象
        self.assertIsInstance(chunk, Chunk)
        self.assertEqual(chunk.event_id, 2)
        self.assertEqual(chunk.event_type, EVENT_TYPE_LOADING)
        self.assertEqual(chunk.package_id, 1)
        self.assertEqual(chunk.package_type, PACKAGE_TYPE_TEXT)
        self.assertEqual(chunk.chunk_id, 1)
        self.assertTrue(chunk.is_last)
        self.assertEqual(chunk.data, test_data)

    def test_end(self):
        """测试 end 方法"""
        # 先调用 start 初始化状态
        self.transmitter.start()

        # 测试结束消息
        chunk = self.transmitter.end()

        # 验证状态变化
        self.assertEqual(self.transmitter.event_id, 1)
        self.assertEqual(self.transmitter.event_type, EVENT_TYPE_END)
        self.assertEqual(self.transmitter.package_id, 1)
        self.assertEqual(self.transmitter.chunk_id, 0)
        self.assertEqual(self.transmitter.package_type, PACKAGE_TYPE_NONE)

        # 验证返回的 chunk 对象
        self.assertIsInstance(chunk, Chunk)
        self.assertEqual(chunk.event_id, 1)
        self.assertEqual(chunk.event_type, EVENT_TYPE_END)
        self.assertEqual(chunk.package_id, 1)
        self.assertEqual(chunk.package_type, PACKAGE_TYPE_NONE)
        self.assertEqual(chunk.chunk_id, 0)
        self.assertTrue(chunk.is_last)
        self.assertIsNone(chunk.data)

    def test_create_chunk(self):
        """测试 create_chunk 方法"""
        # 设置 Transmitter 的状态
        self.transmitter.event_id = 5
        self.transmitter.event_type = EVENT_TYPE_LOADING
        self.transmitter.package_id = 2
        self.transmitter.package_type = PACKAGE_TYPE_TEXT
        self.transmitter.chunk_id = 3

        # 测试创建 chunk
        test_data = "test chunk data"
        chunk = self.transmitter.create_chunk(test_data, is_last=True)

        # 验证返回的 chunk 对象
        self.assertIsInstance(chunk, Chunk)
        self.assertEqual(chunk.event_id, 5)
        self.assertEqual(chunk.event_type, EVENT_TYPE_LOADING)
        self.assertEqual(chunk.package_id, 2)
        self.assertEqual(chunk.package_type, PACKAGE_TYPE_TEXT)
        self.assertEqual(chunk.chunk_id, 3)
        self.assertTrue(chunk.is_last)
        self.assertEqual(chunk.data, test_data)


if __name__ == "__main__":
    unittest.main()
