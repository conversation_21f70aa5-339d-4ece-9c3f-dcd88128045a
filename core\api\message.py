from fastapi import APIRouter, Depends, HTTPException, Query

from core.vo.user_vo import UserVO
from core.config.security import check_user
from core.services.database import db_manager
from core.services.database.crud.message import message_curd
from core.services.database.crud.conversation import conversation_curd

router = APIRouter(prefix="/message")

@router.get("")
async def query_messages(
    conversation_id: str = Query(..., description="会话ID"),
    user: UserVO = Depends(check_user),
):
    """
    根据会话ID查询消息列表

    Args:
        conversation_id: 会话ID
        user: 当前用户信息

    Returns:
        消息列表
    """
    try:
        async with db_manager.session() as session:
            # 如果会话不属于当前用户，抛出异常
            conversation = await conversation_curd.get_by_conversation_id(
                session, _id=conversation_id
            )
            if not conversation:
                raise HTTPException(status_code=404, detail="会话不存在")
            if conversation[0].user_id != user.userId:
                raise HTTPException(status_code=403, detail="会话不属于当前用户")

            # 查询指定会话ID的所有消息
            messages = await message_curd.get_by_conversation_id(
                session, conversation_id=conversation_id
            )

            return {
                "code": 200,
                "data": messages,
                "message": "查询成功",
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e
    