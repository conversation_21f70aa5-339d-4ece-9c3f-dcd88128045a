FROM python:3.11.11-bullseye

# Install the uv and uvx binaries
COPY --from=ghcr.io/astral-sh/uv:0.7.8 /uv /uvx /bin/

# Set the working directory
WORKDIR /app

# Copy the pyproject file
COPY pyproject.toml uv.lock .python-version .

# Install the dependencies
RUN uv sync --frozen

# Copy the rest of the application code
COPY . .

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uv", "run", "main.py"]
