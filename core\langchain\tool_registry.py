
from langchain_core.tools import tool
from typing import List
import logging

# 工具注册器，将MCP的工具注册到LangChain中
# <AUTHOR> xiangjh

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
def register_tools(mcp_client, tools_response):
    tools = []

    for cust_tool in tools_response.tools:
        tool_name = cust_tool.name
        tool_description = cust_tool.description

        def make_tool_func(name: str, desc: str):
            @tool(name)
            async def tool_func(**kwargs):
                """{description}"""
                result = await mcp_client.execute_tool(name, kwargs)
                return result.content

            tool_func.__doc__ = desc
            tool_func.description = desc
            return tool_func

        tool_func = make_tool_func(tool_name, tool_description)
        tools.append(tool_func)
    return tools