from langchain.agents import create_openai_tools_agent,AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from core.config.app_config import ModelConfig
from typing import List, Optional
from core.config.app_logger import logger
# agent 构造器（提示词模版，大模型实例，执行器） 
# <AUTHOR> xiangjh

PROMPT_TEMPLATES = {
    "default": """你是一个智能助手。
                请根据用户输入调用合适的工具，
                并严格按照指定格式返回结果。"""
}

def get_prompt_template_by_agent_id(agent_id: Optional[str]) -> str:
    from core.factory.agent_factory import AgentFactory
    logger.info(f">>>agent_id: {agent_id}")
    try:
        if agent_id:
            agent = AgentFactory.get_agent(agent_id)
            if hasattr(agent, 'prompt_text') and agent.prompt_text:
                return agent.prompt_text
            logger.warning(f"Agent {agent_id} 未配置提示词，使用默认模板")
    except ValueError as e:
        logger.warning(f"获取Agent失败: {str(e)}，使用默认模板")
    
    # 本地默认模板
    return PROMPT_TEMPLATES["default"]

def build_prompt(agent_id: str) -> ChatPromptTemplate:
    system_prompt = get_prompt_template_by_agent_id(agent_id)
    return ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad")
    ])


def build_llm(model_config: ModelConfig):
    return ChatOpenAI(
        model=model_config.name,
        openai_api_key=model_config.api_key,
        openai_api_base=model_config.base_url,
        temperature=model_config.temperature,
        streaming=True
    )

def build_agent_executor(agent_id: str,tools: List, model_config: ModelConfig) -> AgentExecutor:
    """
    构建完整的 AgentExecutor 实例
    """
    prompt = build_prompt(agent_id)
    llm = build_llm(model_config)
    agent = create_openai_tools_agent(llm, tools, prompt)

    logger.info("构建 AgentExecutor 实例")
    return AgentExecutor(
        agent=agent,
        tools=tools,
        return_intermediate_steps=True,
        handle_parsing_errors=True,
        max_iterations=3 , 
        early_stopping_method="generate"
    )