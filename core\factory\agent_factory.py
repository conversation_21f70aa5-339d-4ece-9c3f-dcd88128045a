from typing import Dict, Any, Optional
from core.config.app_config import config
from core.api.dynaimic_assistant import dify_generator_and_send
from core.api.dynaimic_creator import event_generator_and_send
from core.config.app_logger import logger
from core.services.database.crud.ai_agent_dao import ai_agent_crud


class AgentFactory:
    _senders: Dict[str, Any] = {}
    _agents: Dict[str, Any] = {}

    @classmethod
    async def load_agents(cls):
        """从数据库加载 agent 并注册 sender 方法"""
        try:
            # 从数据库获取所有 agent
            agents = await ai_agent_crud.query_agents()
            
            agent_mapping = {}
            for agent in agents:
                try:
                    sender = cls._get_sender_by_target(agent.target_type)
                    agent_mapping[agent.agent_code] = sender
                    cls._agents[agent.agent_code] = agent
                    logger.info(f"成功注册 agent: {agent.agent_name}, target: {agent.target_type}")
                except ValueError as e:
                    logger.warning(f"跳过 agent '{agent.agent_name}': {str(e)}")

            cls.register_senders(agent_mapping)
        except Exception as e:
            logger.error(f"加载 agents 失败: {str(e)}")
            raise

    @classmethod
    def get_sender(cls, agent_code: str) -> Any:
        """
        根据 agent_code 获取对应的 sender 方法。
        
        :param agent_code: Agent 的唯一标识符。
        :return: 匹配的 sender 方法。
        :raises ValueError: 如果未找到匹配的 sender。
        """
        sender = cls._senders.get(agent_code)
        if not sender:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的 sender")
        return sender

    @classmethod
    def get_agent(cls, agent_code: str) -> Any:
        """
        从_agents中获取指定的agent对象
        
        :param agent_code: Agent的唯一标识符
        :return: 对应的agent对象
        :raises ValueError: 如果未找到对应的agent
        """
        agent = cls._agents.get(agent_code)
        if not agent:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的agent")
        return agent

    @classmethod
    def register_sender(cls, agent_code: str, sender: Any):
        cls._senders[agent_code] = sender

    @classmethod
    def register_senders(cls, senders: Dict[str, Any]):
        cls._senders.update(senders)

    @classmethod
    def _get_sender_by_target(cls, target: str):
        """根据 target 字段返回对应的 sender 方法"""
        if target == "dify":
            return dify_generator_and_send
        elif target == "langchain":
            return event_generator_and_send
        else:
            raise ValueError(f"未知的 target 类型: {target}")
    


