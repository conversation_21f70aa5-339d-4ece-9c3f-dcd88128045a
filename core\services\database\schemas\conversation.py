from typing import Optional
from datetime import datetime, timezone
from uuid import uuid4
from sqlalchemy import Column, DateTime
from sqlmodel import Field, SQLModel

class ConversationBase(SQLModel):
    """对话基础模型"""
    id: str = Field(default_factory=lambda: uuid4().hex, primary_key=True, unique=True)
    title: str = Field(default="Untitled", description="会话标题")
    user_id: int = Field(index=True, description="用户ID")
    current_agent_code: str = Field(description="当前活跃的智能体ID")
    # timestamp with timezone
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), sa_column=Column(DateTime(timezone=True)), description="创建时间，ISO 8601 格式")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), sa_column=Column(DateTime(timezone=True)), description="更新时间，ISO 8601 格式")

class ConversationTable(ConversationBase, table=True):
    """数据库中的对话模型"""
    __tablename__ = "conversation"

class ConversationCreate(ConversationBase):
    """创建对话模型"""

class ConversationUpdate(SQLModel):
    title: Optional[str] = None
    updated_at: datetime = datetime.now(timezone.utc)
    """更新对话模型"""

class ConversationInDBBase(ConversationBase):
    """数据库中的对话模型"""
    id: str
    created_at: str

