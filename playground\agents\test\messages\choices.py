radio_group_message = """
<message-embedded>
    <widget>
        <code>@BuildIn/RadioGroupInteractive</code>
        <props>
            <options>
                [
                  {"label": "All", "value": "all"},
                  {"label": "RadioChoices", "value": "radio_choices"},
                  {"label": "SelectChoices", "value": "select_choices"},
                  {"label": "Mermaid", "value": "mermaid"},
                  {"label": "Thinking", "value": "thinking"},
                  {"label": "Tool", "value": "tool"}
                ]
            </options>
    </widget>
</message-embedded>
"""

radio_choices_message_chunks = [
    {
        "data": "请选择消息类型",
        "is_last": False,
        "package_type": 0,
        "is_new_package": True,
    },
    {
        "data": radio_group_message,
        "is_last": True,
        "package_type": 0,
        "is_new_package": False,
    },
]


select_message = """
<message-embedded>
    <widget>
        <code>@BuildIn/SelectInteractive</code>
        <props>
            <options>
                [
                  {"label": "All", "value": "all"},
                  {"label": "RadioChoices", "value": "radio_choices"},
                  {"label": "SelectChoices", "value": "select_choices"},
                  {"label": "Mermaid", "value": "mermaid"},
                  {"label": "Thinking", "value": "thinking"},
                  {"label": "Tool", "value": "tool"}
                ]
            </options>
    </widget>
</message-embedded>
"""

select_message_chunks = [
    {
        "data": "请选择消息类型",
        "is_last": False,
        "package_type": 0,
        "is_new_package": True,
    },
    {
        "data": select_message,
        "is_last": True,
        "package_type": 0,
        "is_new_package": False,
    },
]
