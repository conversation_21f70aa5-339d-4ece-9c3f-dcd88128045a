from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.message import MessageTable
from core.services.database.schemas.message import (
    MessageCreate,
    MessageUpdate,
)


class CRUDMessage(CRUDBase[MessageTable, MessageCreate, MessageUpdate]):
    """消息CRUD操作实现"""

    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[MessageTable]:
        """根据用户ID获取消息列表"""
        query = (
            select(MessageTable)
            .where(MessageTable.user_id == user_id)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_conversation_id(
        self, db: AsyncSession, *, conversation_id: int, skip: int = 0
    ) -> List[MessageTable]:
        """根据对话ID获取消息列表 按照创建时间降序排序"""

        # TODO: 优化为游标查询
        query = (
            select(MessageTable)
            .where(MessageTable.conversation_id == conversation_id)
            .offset(skip)
            .order_by(MessageTable.created_at)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_session_id(
        self, db: AsyncSession, *, session_id: str
    ) -> List[MessageTable]:
        """根据会话ID获取消息"""
        query = (
            select(MessageTable)
            .where(MessageTable.session_id == session_id)
            .order_by(MessageTable.created_at)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_message_type(
        self, db: AsyncSession, *, message_type: str, skip: int = 0, limit: int = 100
    ) -> List[MessageTable]:
        """根据消息类型获取消息列表"""
        query = (
            select(MessageTable)
            .where(MessageTable.message_type == message_type)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()


message_curd = CRUDMessage(MessageTable)
