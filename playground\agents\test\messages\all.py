from .util import create_message_chunks

all_message = """
Hello World

# Mermaid

```mermaid
classDiagram
Class01 <|-- AveryLongClass : Cool
<<Interface>> Class01
Class09 --> C2 : Where am I?
Class09 --* C3
Class09 --|> Class07
Class07 : equals()
Class07 : Object[] elementData
Class01 : size()
Class01 : int chimp
Class01 : int gorilla
class Class10 {
  <<service>>
  int id
  size()
}
```

# GFM

## Autolink literals

www.example.com, https://example.com, and <EMAIL>.

## Footnote

A note[^1]

[^1]: Big note.

## Strikethrough

~one~ or ~~two~~ tildes.

## Table

| a | b  |  c |  d  |
|---|----|----|-----|
| - | :- | -: | :-: |
| 1 |  2 |  3 |  4  |

## Tasklist

* [ ] to do
* [x] done


# 工具调用

<message-embedded>
    <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>查询工具调用</name>
            <result>工具输出结果xxxxx</result>
        </props>
    </widget>
    <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>查询工具调用</name>
            <result>工具输出结果xxxxx</result>
        </props>
    </widget>
</message-embedded>

# Widget组件
<message-embedded>
    <widget>
        <code>@DynamicPage/PreviewButton</code>
        <props>
            <id>111111111111</id>
            <name>测试动态页面</name>
        </props>
    </widget>
</message-embedded>
"""

thinking_message_chunks = [
    {
        "data": "I am",
        "is_last": False,
        "package_type": 2,
        "is_new_package": True,
    },
    {
        "data": "Thinking...",
        "is_last": False,
        "package_type": 2,
        "is_new_package": False,
    },
    {
        "data": "Thinking......",
        "is_last": False,
        "package_type": 2,
        "is_new_package": False,
    },
    {
        "data": "Thinking End",
        "is_last": True,
        "package_type": 2,
        "is_new_package": False,
    },
]


def create_all_message_chunks():
    chunks = thinking_message_chunks + create_message_chunks(all_message)

    return chunks
