from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.conversation import ConversationTable
from core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationUpdate,
)


class CRUDConversation(
    CRUDBase[ConversationTable, ConversationCreate, ConversationUpdate]
):
    """对话记录CRUD操作实现"""

    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """根据用户ID获取对话列表"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.user_id == user_id)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_agent_code(
        self, db: AsyncSession, *, agent_code: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """根据代理ID获取对话列表"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.agent_code == agent_code)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_conversation_id(
        self, db: AsyncSession, *, _id: str
    ) -> List[ConversationTable]:
        """根据会话ID获取完整对话"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.id == _id)
            .order_by(ConversationTable.created_at)
        )
        result = await db.execute(query)
        return result.scalars().all()


conversation_curd = CRUDConversation(ConversationTable)
